{"runtimeTarget": {"name": ".NETCoreApp,Version=v8.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v8.0": {"Costura.Fody/6.0.0": {"dependencies": {"Fody": "6.8.2"}}, "Flurl/4.0.0": {"runtime": {"lib/netstandard2.0/Flurl.dll": {"assemblyVersion": "4.0.0.0", "fileVersion": "4.0.0.0"}}}, "Flurl.Http/4.0.2": {"dependencies": {"Flurl": "4.0.0"}, "runtime": {"lib/net6.0/Flurl.Http.dll": {"assemblyVersion": "4.0.2.0", "fileVersion": "4.0.2.0"}}}, "Fody/6.8.2": {}, "FreeSql/3.5.212": {"runtime": {"lib/netstandard2.1/FreeSql.dll": {"assemblyVersion": "3.5.212.0", "fileVersion": "3.5.212.0"}}}, "FreeSql.Provider.Sqlite/3.5.212": {"dependencies": {"FreeSql": "3.5.212", "System.Data.SQLite.Core": "1.0.119"}, "runtime": {"lib/netstandard2.0/FreeSql.Provider.Sqlite.dll": {"assemblyVersion": "3.5.212.0", "fileVersion": "3.5.212.0"}}}, "Microsoft.Extensions.Configuration/9.0.8": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.8", "Microsoft.Extensions.Primitives": "9.0.8"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}}}, "Microsoft.Extensions.Configuration.Abstractions/9.0.8": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.8"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}}}, "Microsoft.Extensions.Configuration.Binder/9.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.8"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Binder.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "Microsoft.Extensions.Configuration.FileExtensions/9.0.8": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.8", "Microsoft.Extensions.Configuration.Abstractions": "9.0.8", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.8", "Microsoft.Extensions.FileProviders.Physical": "9.0.8", "Microsoft.Extensions.Primitives": "9.0.8"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}}}, "Microsoft.Extensions.Configuration.Json/9.0.8": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.8", "Microsoft.Extensions.Configuration.Abstractions": "9.0.8", "Microsoft.Extensions.Configuration.FileExtensions": "9.0.8", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.8", "System.Text.Json": "9.0.8"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Json.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}}}, "Microsoft.Extensions.DependencyInjection/9.0.8": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.8"}, "runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.8": {"runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}}}, "Microsoft.Extensions.DependencyModel/9.0.0": {"dependencies": {"System.Text.Encodings.Web": "9.0.8", "System.Text.Json": "9.0.8"}, "runtime": {"lib/net8.0/Microsoft.Extensions.DependencyModel.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "Microsoft.Extensions.FileProviders.Abstractions/9.0.8": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.8"}, "runtime": {"lib/net8.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}}}, "Microsoft.Extensions.FileProviders.Physical/9.0.8": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "9.0.8", "Microsoft.Extensions.FileSystemGlobbing": "9.0.8", "Microsoft.Extensions.Primitives": "9.0.8"}, "runtime": {"lib/net8.0/Microsoft.Extensions.FileProviders.Physical.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}}}, "Microsoft.Extensions.FileSystemGlobbing/9.0.8": {"runtime": {"lib/net8.0/Microsoft.Extensions.FileSystemGlobbing.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}}}, "Microsoft.Extensions.Logging/9.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "9.0.8", "Microsoft.Extensions.Logging.Abstractions": "9.0.0", "Microsoft.Extensions.Options": "9.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "Microsoft.Extensions.Logging.Abstractions/9.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.8", "System.Diagnostics.DiagnosticSource": "9.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "Microsoft.Extensions.Options/9.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.8", "Microsoft.Extensions.Primitives": "9.0.8"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Options.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "Microsoft.Extensions.Primitives/9.0.8": {"runtime": {"lib/net8.0/Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}}}, "Newtonsoft.Json/13.0.3": {"runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"assemblyVersion": "13.0.0.0", "fileVersion": "13.0.3.27908"}}}, "Serilog/4.3.0": {"runtime": {"lib/net8.0/Serilog.dll": {"assemblyVersion": "4.3.0.0", "fileVersion": "4.3.0.0"}}}, "Serilog.Extensions.Logging/9.0.2": {"dependencies": {"Microsoft.Extensions.Logging": "9.0.0", "Serilog": "4.3.0"}, "runtime": {"lib/net8.0/Serilog.Extensions.Logging.dll": {"assemblyVersion": "9.0.2.0", "fileVersion": "9.0.2.0"}}}, "Serilog.Settings.Configuration/9.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Binder": "9.0.0", "Microsoft.Extensions.DependencyModel": "9.0.0", "Serilog": "4.3.0"}, "runtime": {"lib/net8.0/Serilog.Settings.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Sinks.Console/6.0.0": {"dependencies": {"Serilog": "4.3.0"}, "runtime": {"lib/net8.0/Serilog.Sinks.Console.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.0.0"}}}, "Serilog.Sinks.File/7.0.0": {"dependencies": {"Serilog": "4.3.0"}, "runtime": {"lib/net8.0/Serilog.Sinks.File.dll": {"assemblyVersion": "7.0.0.0", "fileVersion": "7.0.0.0"}}}, "Stub.System.Data.SQLite.Core.NetStandard/1.0.119": {"runtime": {"lib/netstandard2.1/System.Data.SQLite.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}, "runtimeTargets": {"runtimes/linux-x64/native/SQLite.Interop.dll": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx-x64/native/SQLite.Interop.dll": {"rid": "osx-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/SQLite.Interop.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "*********"}, "runtimes/win-x86/native/SQLite.Interop.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "*********"}}}, "System.Data.SQLite.Core/1.0.119": {"dependencies": {"Stub.System.Data.SQLite.Core.NetStandard": "1.0.119"}}, "System.Diagnostics.DiagnosticSource/9.0.0": {"runtime": {"lib/net8.0/System.Diagnostics.DiagnosticSource.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "System.IO.Pipelines/9.0.8": {"runtime": {"lib/net8.0/System.IO.Pipelines.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}}}, "System.Text.Encodings.Web/9.0.8": {"runtime": {"lib/net8.0/System.Text.Encodings.Web.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}}, "runtimeTargets": {"runtimes/browser/lib/net8.0/System.Text.Encodings.Web.dll": {"rid": "browser", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}}}, "System.Text.Json/9.0.8": {"dependencies": {"System.IO.Pipelines": "9.0.8", "System.Text.Encodings.Web": "9.0.8"}, "runtime": {"lib/net8.0/System.Text.Json.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}}}}}, "libraries": {"Costura.Fody/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3Uriu9GJABMivG0wXMJs6NQ7FNE3pylir1gZEBAWDvpii3cnrmxXnOG44MMDuIVOIk/Xhef7WZFsaCNV+py9qA==", "path": "costura.fody/6.0.0", "hashPath": "costura.fody.6.0.0.nupkg.sha512"}, "Flurl/4.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-rpts69yYgvJqg6PPgqShBQEZ4aNzWQqWpWppcT0oDWxDCIsBqiod4pj6LQZdhk+1OozLFagemldMRACdHF3CsA==", "path": "flurl/4.0.0", "hashPath": "flurl.4.0.0.nupkg.sha512"}, "Flurl.Http/4.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-9vCqFFyceA11yplkFD8AbCFFTvG1Lrw3tpsgOpL5sLUc28p6zcvGszNleuT6nDymRvtt5eS+rqUX+bRztg1fhA==", "path": "flurl.http/4.0.2", "hashPath": "flurl.http.4.0.2.nupkg.sha512"}, "Fody/6.8.2": {"type": "package", "serviceable": true, "sha512": "sha512-sjGHrtGS1+kcrv99WXCvujOFBTQp4zCH3ZC9wo2LAtVaJkuLpHghQx3y4k1Q8ZKuDAbEw+HE6ZjPUJQK3ejepQ==", "path": "fody/6.8.2", "hashPath": "fody.6.8.2.nupkg.sha512"}, "FreeSql/3.5.212": {"type": "package", "serviceable": true, "sha512": "sha512-8QJlbW0khtPKzWGzXonp5I8mEPrPVrouYCdFWr3cAtAUjlnj+qb0JYMj3rPFN55ZN4cvP2eu2j1LiFLERqQNZQ==", "path": "freesql/3.5.212", "hashPath": "freesql.3.5.212.nupkg.sha512"}, "FreeSql.Provider.Sqlite/3.5.212": {"type": "package", "serviceable": true, "sha512": "sha512-wAJ0CefIhLizdCtCnxGi0U20Qvm09L57+A/ogdsgFWBSEusGUVUJnGm2eXOu0SuEplEwWH+da4a7dUYde8if4A==", "path": "freesql.provider.sqlite/3.5.212", "hashPath": "freesql.provider.sqlite.3.5.212.nupkg.sha512"}, "Microsoft.Extensions.Configuration/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-6m+8Xgmf8UWL0p/oGqBM+0KbHE5/ePXbV1hKXgC59zEv0aa0DW5oiiyxDbK5kH5j4gIvyD5uWL0+HadKBJngvQ==", "path": "microsoft.extensions.configuration/9.0.8", "hashPath": "microsoft.extensions.configuration.9.0.8.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-yNou2KM35RvzOh4vUFtl2l33rWPvOCoba+nzEDJ+BgD8aOL/jew4WPCibQvntRfOJ2pJU8ARygSMD+pdjvDHuA==", "path": "microsoft.extensions.configuration.abstractions/9.0.8", "hashPath": "microsoft.extensions.configuration.abstractions.9.0.8.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Binder/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-RiScL99DcyngY9zJA2ROrri7Br8tn5N4hP4YNvGdTN/bvg1A3dwvDOxHnNZ3Im7x2SJ5i4LkX1uPiR/MfSFBLQ==", "path": "microsoft.extensions.configuration.binder/9.0.0", "hashPath": "microsoft.extensions.configuration.binder.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.FileExtensions/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-2jgx58Jpk3oKT7KRn8x/cFf3QDTjQP+KUbyBnynAcB2iBx1Eq9EdNMCu0QEbYuaZOaQru/Kwdffary+hn58Wwg==", "path": "microsoft.extensions.configuration.fileextensions/9.0.8", "hashPath": "microsoft.extensions.configuration.fileextensions.9.0.8.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Json/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-vjxzcnL7ul322+kpvELisXaZl8/5MYs6JfI9DZLQWsao1nA/4FL48yPwDK986hbJTWc64JxOOaMym0SQ/dy32w==", "path": "microsoft.extensions.configuration.json/9.0.8", "hashPath": "microsoft.extensions.configuration.json.9.0.8.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-JJjI2Fa+QtZcUyuNjbKn04OjIUX5IgFGFu/Xc+qvzh1rXdZHLcnqqVXhR4093bGirTwacRlHiVg1XYI9xum6QQ==", "path": "microsoft.extensions.dependencyinjection/9.0.8", "hashPath": "microsoft.extensions.dependencyinjection.9.0.8.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-xY3lTjj4+ZYmiKIkyWitddrp1uL5uYiweQjqo4BKBw01ZC4HhcfgLghDpPZcUlppgWAFqFy9SgkiYWOMx365pw==", "path": "microsoft.extensions.dependencyinjection.abstractions/9.0.8", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.9.0.8.nupkg.sha512"}, "Microsoft.Extensions.DependencyModel/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-saxr2XzwgDU77LaQfYFXmddEDRUKHF4DaGMZkNB3qjdVSZlax3//dGJagJkKrGMIPNZs2jVFXITyCCR6UHJNdA==", "path": "microsoft.extensions.dependencymodel/9.0.0", "hashPath": "microsoft.extensions.dependencymodel.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Abstractions/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-4zZbQ4w+hCMm9J+z5NOj3giIPT2MhZxx05HX/MGuAmDBbjOuXlYIIRN+t4V6OLxy5nXZIcXO+dQMB/OWubuDkw==", "path": "microsoft.extensions.fileproviders.abstractions/9.0.8", "hashPath": "microsoft.extensions.fileproviders.abstractions.9.0.8.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Physical/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-FlOe2i7UUIfY0l0ChaIYtlXjdWWutR4DMRKZaGD6z4G1uVTteFkbBfxUIoi1uGmrZQxXe/yv/cfwiT0tK2xyXA==", "path": "microsoft.extensions.fileproviders.physical/9.0.8", "hashPath": "microsoft.extensions.fileproviders.physical.9.0.8.nupkg.sha512"}, "Microsoft.Extensions.FileSystemGlobbing/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-96Ub5LmwYfIGVoXkbe4kjs+ivK6fLBTwKJAOMfUNV0R+AkZRItlgROFqXEWMUlXBTPM1/kKu26Ueu5As6RDzJA==", "path": "microsoft.extensions.filesystemglobbing/9.0.8", "hashPath": "microsoft.extensions.filesystemglobbing.9.0.8.nupkg.sha512"}, "Microsoft.Extensions.Logging/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-crj<PERSON>yORoug0kK7RSNJBTeSE6VX8IQgLf3nUpTB9m62bPXp/tzbnOsnbe8TXEG0AASNaKZddnpHKw7fET8E++Pg==", "path": "microsoft.extensions.logging/9.0.0", "hashPath": "microsoft.extensions.logging.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-g0UfujELzlLbHoVG8kPKVBaW470Ewi+jnptGS9KUi6jcb+k2StujtK3m26DFSGGwQ/+bVgZfsWqNzlP6YOejvw==", "path": "microsoft.extensions.logging.abstractions/9.0.0", "hashPath": "microsoft.extensions.logging.abstractions.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Options/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-y2146b3jrPI3Q0lokKXdKLpmXqakYbDIPDV6r3M8SqvSf45WwOTzkyfDpxnZXJsJQEpAsAqjUq5Pu8RCJMjubg==", "path": "microsoft.extensions.options/9.0.0", "hashPath": "microsoft.extensions.options.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Primitives/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-tizSIOEsIgSNSSh+hKeUVPK7xmTIjR8s+mJWOu1KXV3htvNQiPMFRMO17OdI1y/4ZApdBVk49u/08QGC9yvLug==", "path": "microsoft.extensions.primitives/9.0.8", "hashPath": "microsoft.extensions.primitives.9.0.8.nupkg.sha512"}, "Newtonsoft.Json/13.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "path": "newtonsoft.json/13.0.3", "hashPath": "newtonsoft.json.13.0.3.nupkg.sha512"}, "Serilog/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-+cDryFR0GRhsGOnZSKwaDzRRl4MupvJ42FhCE4zhQRVanX0Jpg6WuCBk59OVhVDPmab1bB+nRykAnykYELA9qQ==", "path": "serilog/4.3.0", "hashPath": "serilog.4.3.0.nupkg.sha512"}, "Serilog.Extensions.Logging/9.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-p8kk2McN6LxuQfLyCoOkL7+nJIhVKnV1WFUxAaGTQTQk0wySbgmCHe98j+xSQvIbYHtzKXROOE2G2R0TLwBfig==", "path": "serilog.extensions.logging/9.0.2", "hashPath": "serilog.extensions.logging.9.0.2.nupkg.sha512"}, "Serilog.Settings.Configuration/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-4/Et4Cqwa+F88l5SeFeNZ4c4Z6dEAIKbu3MaQb2Zz9F/g27T5a3wvfMcmCOaAiACjfUb4A6wrlTVfyYUZk3RRQ==", "path": "serilog.settings.configuration/9.0.0", "hashPath": "serilog.settings.configuration.9.0.0.nupkg.sha512"}, "Serilog.Sinks.Console/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-fQGWqVMClCP2yEyTXPIinSr5c+CBGUvBybPxjAGcf7ctDhadFhrQw03Mv8rJ07/wR5PDfFjewf2LimvXCDzpbA==", "path": "serilog.sinks.console/6.0.0", "hashPath": "serilog.sinks.console.6.0.0.nupkg.sha512"}, "Serilog.Sinks.File/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-fKL7mXv7qaiNBUC71ssvn/dU0k9t0o45+qm2XgKAlSt19xF+ijjxyA3R6HmCgfKEKwfcfkwWjayuQtRueZFkYw==", "path": "serilog.sinks.file/7.0.0", "hashPath": "serilog.sinks.file.7.0.0.nupkg.sha512"}, "Stub.System.Data.SQLite.Core.NetStandard/1.0.119": {"type": "package", "serviceable": true, "sha512": "sha512-dI7ngiCNgdm+n00nQvFTa+LbHvE9MIQXwMSLRzJI/KAJ7G1WmCachsvfE1CD6xvb3OXJvYYEfv3+S/LHyhN0Rg==", "path": "stub.system.data.sqlite.core.netstandard/1.0.119", "hashPath": "stub.system.data.sqlite.core.netstandard.1.0.119.nupkg.sha512"}, "System.Data.SQLite.Core/1.0.119": {"type": "package", "serviceable": true, "sha512": "sha512-bhQB8HVtRA+OOYw8UTD1F1kU+nGJ0/OZvH1JmlVUI4bGvgVEWeX1NcHjA765NvUoRVuCPlt8PrEpZ1thSsk1jg==", "path": "system.data.sqlite.core/1.0.119", "hashPath": "system.data.sqlite.core.1.0.119.nupkg.sha512"}, "System.Diagnostics.DiagnosticSource/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ddppcFpnbohLWdYKr/ZeLZHmmI+DXFgZ3Snq+/E7SwcdW4UnvxmaugkwGywvGVWkHPGCSZjCP+MLzu23AL5SDw==", "path": "system.diagnostics.diagnosticsource/9.0.0", "hashPath": "system.diagnostics.diagnosticsource.9.0.0.nupkg.sha512"}, "System.IO.Pipelines/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-6vPmJt73mgUo1gzc/OcXlJvClz/2jxZ4TQPRfriVaLoGRH2mye530D9WHJYbFQRNMxF3PWCoeofsFdCyN7fLzA==", "path": "system.io.pipelines/9.0.8", "hashPath": "system.io.pipelines.9.0.8.nupkg.sha512"}, "System.Text.Encodings.Web/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-W+LotQsM4wBJ4PG7uRkyul4wqL4Fz7R4ty6uXrCNZUhbaHYANgrPaYR2ZpMVpdCjQEJ17Jr1NMN8hv4SHaHY4A==", "path": "system.text.encodings.web/9.0.8", "hashPath": "system.text.encodings.web.9.0.8.nupkg.sha512"}, "System.Text.Json/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-mIQir9jBqk0V7X0Nw5hzPJZC8DuGdf+2DS3jAVsr6rq5+/VyH5rza0XGcONJUWBrZ+G6BCwNyjWYd9lncBu48A==", "path": "system.text.json/9.0.8", "hashPath": "system.text.json.9.0.8.nupkg.sha512"}}}