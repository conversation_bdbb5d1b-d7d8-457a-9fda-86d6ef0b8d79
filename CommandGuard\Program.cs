using CommandGuard.Configuration;
using CommandGuard.Forms;
using CommandGuard.Helpers;
using CommandGuard.Interfaces.Business;
using CommandGuard.Interfaces.Chat;
using CommandGuard.Interfaces.Infrastructure;
using CommandGuard.Interfaces.Lottery;
using CommandGuard.Services.Business;
using CommandGuard.Services.Chat;
using CommandGuard.Services.Infrastructure;
using CommandGuard.Services.Lottery;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Serilog;
using WinFormsApplication = System.Windows.Forms.Application;

namespace CommandGuard;

/// <summary>
/// 应用程序主入口类
/// 负责应用程序的启动、配置和依赖注入容器的初始化
/// 采用现代.NET开发模式，集成配置管理、日志记录和依赖注入
/// 为WinForms应用程序提供企业级的架构支持
/// </summary>
static class Program
{
    #region 主入口点

    /// <summary>
    /// 应用程序主入口点
    /// 按照以下顺序初始化：异常处理 → 日志系统 → 依赖注入 → UI启动
    /// </summary>
    [STAThread]
    static async Task Main()
    {
        if (DateTime.Now > new DateTime(2025, 8, 25))
        {
            return;
        }

        try
        {
            // 第一步：设置全局异常处理
            SetupGlobalExceptionHandling();

            // 第二步：初始化日志系统
            InitializeLogging();

            // 第三步：启动应用程序核心
            await StartApplicationAsync();
        }
        catch (Exception ex)
        {
            // 记录启动失败的详细信息
            Log.Fatal(ex, @"应用程序启动失败");

            // 向用户显示友好的错误消息
            MessageBox.Show($@"应用程序启动失败: {ex.Message}", @"错误",
                MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
        finally
        {
            // 确保日志系统正确关闭
            await CleanupResourcesAsync();
        }
    }

    #endregion

    #region 应用程序启动流程

    /// <summary>
    /// 启动应用程序核心流程
    /// </summary>
    private static async Task StartApplicationAsync()
    {
        Log.Information(@"应用程序启动开始");

        // 1. 初始化WinForms应用程序配置
        ApplicationConfiguration.Initialize();

        // 2. 配置依赖注入容器
        var serviceProvider = await SetupDependencyInjectionAsync();

        // 3. 启动用户界面
        StartUserInterface(serviceProvider);

        Log.Information(@"应用程序正常退出");
    }

    /// <summary>
    /// 配置依赖注入容器
    /// </summary>
    private static Task<ServiceProvider> SetupDependencyInjectionAsync()
    {
        var services = new ServiceCollection();
        ConfigureServices(services);

        var serviceProvider = services.BuildServiceProvider();
        Log.Information(@"依赖注入容器配置完成，已注册 {ServiceCount} 个服务", services.Count);

        // 设置全局服务提供者引用，用于程序关闭时释放资源
        RuntimeConfiguration.ServiceProvider = serviceProvider;

        return Task.FromResult(serviceProvider);
    }

    /// <summary>
    /// 启动用户界面
    /// </summary>
    private static void StartUserInterface(ServiceProvider serviceProvider)
    {
        // 设置应用程序退出事件处理
        SetupApplicationExitHandling();

        // 启动主窗体
        var formStartup = serviceProvider.GetRequiredService<FormStartup>();
        Log.Information(@"启动配置窗体创建成功，开始运行应用程序");

        WinFormsApplication.Run(formStartup);
    }

    #endregion

    #region 初始化方法

    /// <summary>
    /// 设置全局异常处理
    /// </summary>
    private static void SetupGlobalExceptionHandling()
    {
        WinFormsApplication.SetUnhandledExceptionMode(UnhandledExceptionMode.CatchException);
        WinFormsApplication.ThreadException += Application_ThreadException;
        AppDomain.CurrentDomain.UnhandledException += CurrentDomain_UnhandledException;
    }

    /// <summary>
    /// 初始化日志系统
    /// </summary>
    private static void InitializeLogging()
    {
        try
        {
            Log.Logger = new LoggerConfiguration()
                .ReadFrom.Configuration(BuildConfiguration())
                .CreateLogger();
        }
        catch (Exception ex)
        {
            // 如果日志配置失败，使用基本配置
            Log.Logger = new LoggerConfiguration()
                .WriteTo.Console()
                .WriteTo.File(@"logs/fallback-.log", rollingInterval: RollingInterval.Day)
                .CreateLogger();

            Log.Warning(ex, @"使用配置文件初始化日志失败，已切换到备用日志配置");
        }
    }

    /// <summary>
    /// 设置应用程序退出事件处理
    /// </summary>
    private static void SetupApplicationExitHandling()
    {
        WinFormsApplication.ApplicationExit += async (_, _) =>
        {
            Log.Information(@"应用程序正在退出，开始清理全局资源");

            try
            {
                // 取消全局的后台任务
                RuntimeConfiguration.RobotServiceCancellation?.Cancel();

                // 等待更长时间让任务完成清理
                Log.Information(@"等待后台任务完成清理...");
                await Task.Delay(5000);

                // 释放FreeSql资源
                try
                {
                    var serviceProvider = RuntimeConfiguration.ServiceProvider;
                    if (serviceProvider != null)
                    {
                        var freeSql = serviceProvider.GetService<IFreeSql>();
                        if (freeSql != null)
                        {
                            Log.Information(@"正在释放FreeSql数据库连接池...");
                            freeSql.Dispose();
                            Log.Information(@"FreeSql资源已释放");
                        }
                    }
                }
                catch (Exception ex)
                {
                    Log.Warning(ex, @"释放FreeSql资源时发生异常");
                }

                // 强制垃圾回收
                GC.Collect();
                GC.WaitForPendingFinalizers();
                GC.Collect();

                Log.Information(@"全局资源清理完成");

                // 如果程序仍未退出，强制终止
                Log.Information(@"检查是否需要强制终止进程...");
                await Task.Delay(500);

                // 强制终止进程
                Log.Warning(@"强制终止进程以确保完全退出");
                Environment.Exit(0);
            }
            catch (Exception ex)
            {
                Log.Error(ex, @"清理全局资源时发生异常");

                // 即使发生异常也要强制退出
                Log.Warning(@"发生异常，强制终止进程");
                Environment.Exit(1);
            }
        };
    }

    /// <summary>
    /// 清理资源
    /// </summary>
    private static async Task CleanupResourcesAsync()
    {
        try
        {
            await Log.CloseAndFlushAsync();
        }
        catch (Exception ex)
        {
            Console.WriteLine($@"关闭日志系统时发生异常: {ex.Message}");
        }
    }

    #endregion

    #region 配置方法

    /// <summary>
    /// 构建应用程序配置
    /// 从appsettings.json文件加载配置信息
    /// 支持配置文件的热重载功能
    /// </summary>
    /// <returns>配置对象，包含所有应用程序设置</returns>
    private static IConfiguration BuildConfiguration()
    {
        // 获取应用程序的基础目录（exe文件所在目录）
        var basePath = AppContext.BaseDirectory;

        return new ConfigurationBuilder()
            .SetBasePath(basePath) // 设置配置文件的基础路径
            .AddJsonFile(Path.Combine("Config", "appsettings.json"), optional: false, reloadOnChange: true) // 核心应用配置
            .AddJsonFile(Path.Combine("Config", "database.json"), optional: false, reloadOnChange: true) // 数据库配置
            .AddJsonFile(Path.Combine("Config", "logging.json"), optional: false, reloadOnChange: true) // 日志配置
            .Build();
    }

    /// <summary>
    /// 配置依赖注入服务容器
    /// 注册所有应用程序需要的服务和组件
    /// 采用不同的生命周期管理策略优化性能和资源使用
    /// </summary>
    /// <param name="services">服务集合，用于注册各种服务</param>
    private static void ConfigureServices(IServiceCollection services)
    {
        // 注册配置服务
        var configuration = BuildConfiguration();
        services.AddSingleton(configuration);

        // 绑定强类型配置，并验证配置的有效性
        var appSettings = new AppSettings();
        var databaseConfig = new DatabaseConfig();

        configuration.Bind(appSettings);
        configuration.Bind(databaseConfig);

        // 验证关键配置项
        if (string.IsNullOrWhiteSpace(databaseConfig.ConnectionStrings.DefaultConnection))
        {
            throw new InvalidOperationException("数据库连接字符串不能为空，请检查database.json中的ConnectionStrings.DefaultConnection配置");
        }


        // 配置强类型配置绑定
        services.Configure<AppSettings>(options => configuration.Bind(options));
        services.Configure<DatabaseConfig>(options => configuration.Bind(options));
        services.Configure<LoggingConfig>(options => configuration.Bind(options));

        // 注册日志服务
        // 清除默认的日志提供程序，使用Serilog
        services.AddLogging(builder =>
        {
            builder.ClearProviders();
            builder.AddSerilog(); // 添加Serilog作为日志提供程序
        });

        // 注册数据库服务,使用FreeSql访问SQLite数据库，添加错误处理和重试机制
        try
        {
            IFreeSql fSql = new FreeSql.FreeSqlBuilder()
                .UseConnectionString(FreeSql.DataType.Sqlite, databaseConfig.ConnectionStrings.DefaultConnection)
                .UseAdoConnectionPool(true)
                // 根据环境决定是否启用SQL监控（生产环境应禁用）
                .UseMonitorCommand(cmd =>
                {
                    if (databaseConfig.Database.EnableSqlMonitoring)
                    {
                        Console.WriteLine($@"Sql：{cmd.CommandText}");
                    }
                })
                .UseAutoSyncStructure(databaseConfig.Database.EnableAutoSyncStructure) // 从配置读取
                .Build();

            // 测试数据库连接
            fSql.Ado.ExecuteNonQuery(@"SELECT 1");

            services.AddSingleton(fSql);
            Log.Information("数据库服务初始化成功");
        }
        catch (Exception ex)
        {
            Log.Fatal(ex, "数据库服务初始化失败");
            throw new InvalidOperationException("数据库服务初始化失败，请检查数据库连接配置", ex);
        }


        // ========================================
        // 第一层：基础设施服务（无依赖或仅依赖数据库/配置）
        // ========================================

        // 系统设置服务 - 基础配置服务，其他服务可能依赖它
        services.AddSingleton<ISystemSettingService, SystemSettingService>();

        // 消息存储服务 - 基础数据服务
        services.AddSingleton<IMessageStorageService, MessageStorageService>();

        // 彩票配置服务 - 基础配置服务
        services.AddSingleton<ILotteryConfigurationService, LotteryConfigurationService>();

        // 赔率服务 - 基础配置服务
        services.AddSingleton<IOddsService, OddsService>();

        // 期号时间服务 - 基础时间服务
        services.AddSingleton<IIssueTimeService, IssueTimeService>();

        // 开奖服务 - 依赖彩票配置和系统设置
        services.AddSingleton<IDrawService, DrawService>();

        // 结算服务 - 游戏结算逻辑服务
        services.AddSingleton<SettlementService>();

        // ========================================
        // 第二层：核心业务服务（依赖基础设施服务）
        // ========================================

        // 会员服务 - 依赖系统设置服务
        services.AddSingleton<IMemberService, MemberService>();

        // 财务服务 - 依赖会员服务
        services.AddSingleton<IFinancialService, FinancialService>();

        // 投注记录服务 - 依赖财务服务和会员服务
        services.AddSingleton<IBetRecordService, BetRecordService>();

        // 上下分记录服务 - 依赖财务服务
        services.AddSingleton<IDepositWithdrawRecordService, DepositWithdrawRecordService>();

        // 输赢返水记录服务 - 依赖会员服务和投注记录服务
        services.AddSingleton<IWinLossRebateRecordService, WinLossRebateRecordService>();

        // 拉手返点记录服务 - 依赖会员服务和投注记录服务
        services.AddSingleton<IAgentRebateRecordService, AgentRebateRecordService>();

        // 投注指令验证服务 - 基础验证服务
        services.AddSingleton<IBetCommandValidatorService, BetCommandValidatorService>();

        // 撤销投注服务 - 依赖财务服务和聊天服务
        services.AddSingleton<CancelBetService>();

        // ========================================
        // 第三层：聊天平台服务
        // ========================================

        // 聊天平台具体实现 - 无依赖的平台适配器
        services.AddSingleton<MyQqPlatformService>();
        services.AddSingleton<OneChatPlatformService>();

        // 聊天服务 - 依赖具体的聊天平台服务
        services.AddSingleton<IChatService, ChatService>();

        // ========================================
        // 第四层：高级业务服务（依赖多个核心服务）
        // ========================================

        // 指令服务 - 依赖多个业务服务
        services.AddSingleton<ICommandService, CommandService>();

        // HTTP API服务 - 对外接口服务
        services.AddSingleton<IHttpApiService, HttpApiService>();

        // 系统管理服务 - 依赖赔率服务和系统设置服务
        services.AddSingleton<ISystemManagementService, SystemManagementService>();

        // ========================================
        // 第五层：辅助工具服务
        // ========================================

        // 图片生成服务 - 依赖彩票配置和系统设置
        services.AddSingleton<ImageHelper>();

        // ========================================
        // 第六层：核心机器人服务（依赖所有业务服务）
        // ========================================

        // 机器人服务 - 核心业务协调服务，依赖大部分其他服务
        services.AddSingleton<IRobotService, RobotService>();

        // ========================================
        // 第七层：用户界面层（瞬态生命周期）
        // ========================================

        // 窗体服务 - 每次请求都创建新实例，适合UI组件
        services.AddTransient<FormMain>(serviceProvider =>
        {
            return new FormMain(
                serviceProvider.GetRequiredService<ILogger<FormMain>>(),
                serviceProvider.GetRequiredService<IMemberService>(),
                serviceProvider.GetRequiredService<IChatService>(),
                serviceProvider.GetRequiredService<IRobotService>(),
                serviceProvider.GetRequiredService<IIssueTimeService>(),
                serviceProvider.GetRequiredService<IHttpApiService>(),
                serviceProvider.GetRequiredService<IBetRecordService>(),
                serviceProvider.GetRequiredService<IDepositWithdrawRecordService>(),
                serviceProvider.GetRequiredService<IWinLossRebateRecordService>(),
                serviceProvider.GetRequiredService<IAgentRebateRecordService>(),
                serviceProvider.GetRequiredService<IDrawService>(),
                serviceProvider.GetRequiredService<ISystemManagementService>(),
                serviceProvider.GetRequiredService<IOddsService>(),
                serviceProvider.GetRequiredService<ISystemSettingService>(),
                serviceProvider.GetRequiredService<SettlementService>(),
                serviceProvider.GetRequiredService<IFinancialService>(),
                serviceProvider, // 传入ServiceProvider本身
                serviceProvider.GetRequiredService<IFreeSql>());
        });
        services.AddTransient<FormStartup>();
    }

    /// <summary>
    /// 处理UI线程未捕获的异常
    /// </summary>
    private static void Application_ThreadException(object sender, ThreadExceptionEventArgs e)
    {
        Log.Error(e.Exception, @"UI线程发生未捕获的异常");

        var result = MessageBox.Show(
            $@"应用程序遇到错误：{e.Exception.Message}{Environment.NewLine}{Environment.NewLine}是否继续运行？",
            @"应用程序错误",
            MessageBoxButtons.YesNo,
            MessageBoxIcon.Error);

        if (result == DialogResult.No)
        {
            WinFormsApplication.Exit();
        }
    }

    /// <summary>
    /// 处理非UI线程未捕获的异常
    /// </summary>
    private static void CurrentDomain_UnhandledException(object sender, UnhandledExceptionEventArgs e)
    {
        if (e.ExceptionObject is Exception ex)
        {
            Log.Fatal(ex, @"应用程序域发生未捕获的异常，即将终止：{IsTerminating}", e.IsTerminating);
        }
        else
        {
            Log.Fatal(@"应用程序域发生未知类型的未捕获异常，即将终止：{IsTerminating}", e.IsTerminating);
        }

        if (e.IsTerminating)
        {
            MessageBox.Show(@"应用程序遇到严重错误，即将退出。", @"严重错误",
                MessageBoxButtons.OK, MessageBoxIcon.Stop);
        }
    }

    #endregion
}